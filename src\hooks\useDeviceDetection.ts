import { useState, useEffect } from 'react';

/**
 * Types d'appareils détectés
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

/**
 * Informations sur l'appareil détecté
 */
export interface DeviceInfo {
  type: DeviceType;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  hasCamera: boolean;
  hasTouchScreen: boolean;
  screenWidth: number;
  screenHeight: number;
  userAgent: string;
}

/**
 * Hook personnalisé pour détecter le type d'appareil et ses capacités
 * 
 * @returns {DeviceInfo} Informations détaillées sur l'appareil
 */
export const useDeviceDetection = (): DeviceInfo => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    // Valeurs par défaut pour le SSR
    return {
      type: 'desktop',
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      hasCamera: false,
      hasTouchScreen: false,
      screenWidth: 1920,
      screenHeight: 1080,
      userAgent: ''
    };
  });

  useEffect(() => {
    const detectDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      // Détection du type d'appareil basée sur la largeur d'écran et l'user agent
      const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
      const isTabletUA = /ipad|android(?!.*mobile)/i.test(userAgent);
      
      // Détection basée sur la taille d'écran
      const isMobileScreen = screenWidth < 768;
      const isTabletScreen = screenWidth >= 768 && screenWidth < 1024;
      const isDesktopScreen = screenWidth >= 1024;

      // Combinaison des détections
      let deviceType: DeviceType;
      let isMobile: boolean;
      let isTablet: boolean;
      let isDesktop: boolean;

      if (isMobileUA || isMobileScreen) {
        deviceType = 'mobile';
        isMobile = true;
        isTablet = false;
        isDesktop = false;
      } else if (isTabletUA || isTabletScreen) {
        deviceType = 'tablet';
        isMobile = false;
        isTablet = true;
        isDesktop = false;
      } else {
        deviceType = 'desktop';
        isMobile = false;
        isTablet = false;
        isDesktop = true;
      }

      // Détection des capacités de l'appareil
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // Détection de la caméra (approximative)
      const hasCamera = isMobile || isTablet || 'mediaDevices' in navigator;

      const newDeviceInfo: DeviceInfo = {
        type: deviceType,
        isMobile,
        isTablet,
        isDesktop,
        hasCamera,
        hasTouchScreen,
        screenWidth,
        screenHeight,
        userAgent
      };

      setDeviceInfo(newDeviceInfo);

      // Log pour le débogage
      console.log('🔍 Détection d\'appareil:', {
        type: deviceType,
        dimensions: `${screenWidth}x${screenHeight}`,
        userAgent: userAgent.substring(0, 50) + '...',
        capacités: {
          camera: hasCamera,
          touch: hasTouchScreen
        }
      });
    };

    // Détection initiale
    detectDevice();

    // Écouter les changements de taille d'écran (rotation, redimensionnement)
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // Nettoyage
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  return deviceInfo;
};

/**
 * Hook simplifié pour obtenir uniquement le type d'appareil
 * 
 * @returns {DeviceType} Type d'appareil détecté
 */
export const useDeviceType = (): DeviceType => {
  const { type } = useDeviceDetection();
  return type;
};

/**
 * Hook pour vérifier si l'appareil est mobile
 * 
 * @returns {boolean} True si l'appareil est mobile
 */
export const useIsMobile = (): boolean => {
  const { isMobile } = useDeviceDetection();
  return isMobile;
};

/**
 * Hook pour vérifier si l'appareil a une caméra
 * 
 * @returns {boolean} True si l'appareil a probablement une caméra
 */
export const useHasCamera = (): boolean => {
  const { hasCamera } = useDeviceDetection();
  return hasCamera;
};
