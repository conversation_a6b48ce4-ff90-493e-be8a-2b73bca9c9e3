import React, { useState } from 'react';
import { GooglePhotosPublicService } from '../../services/googlePhotosPublicService';
import { GooglePhoto } from '../../services/googlePhotosService';
import { Button } from '../common/Button';
import { motion, AnimatePresence } from 'framer-motion';

interface PublicAlbumSelectorProps {
  onPhotosLoaded?: (photos: GooglePhoto[]) => void;
  onPhotoSelect?: (photoId: string) => void;
  selectedPhotos?: Set<string>;
  className?: string;
}

/**
 * Composant pour sélectionner des photos depuis un album public Google Photos
 */
export const PublicAlbumSelector: React.FC<PublicAlbumSelectorProps> = ({
  onPhotosLoaded,
  onPhotoSelect,
  selectedPhotos = new Set(),
  className = ''
}) => {
  const [albumUrl, setAlbumUrl] = useState('');
  const [photos, setPhotos] = useState<GooglePhoto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Charger les photos depuis l'album public
   */
  const loadAlbumPhotos = async () => {
    if (!albumUrl.trim()) {
      setError('Veuillez saisir un lien d\'album Google Photos');
      return;
    }

    // Valider le format du lien
    if (!GooglePhotosPublicService.isValidGooglePhotosUrl(albumUrl)) {
      setError('Format de lien invalide. Utilisez un lien de type: https://photos.app.goo.gl/...');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Chargement de l\'album public:', albumUrl);

      // Extraire l'ID de l'album
      const albumId = GooglePhotosPublicService.extractAlbumId(albumUrl);
      if (!albumId) {
        throw new Error('Impossible d\'extraire l\'ID de l\'album depuis le lien');
      }

      // Récupérer les photos
      const albumPhotos = await GooglePhotosPublicService.fetchAlbumPhotos(albumId);
      
      if (albumPhotos.length === 0) {
        setError('Aucune photo trouvée dans cet album. Vérifiez que l\'album est public et contient des photos.');
        return;
      }

      console.log('✅ Photos chargées:', albumPhotos.length);
      setPhotos(albumPhotos);
      
      // Notifier le parent
      if (onPhotosLoaded) {
        onPhotosLoaded(albumPhotos);
      }

    } catch (err) {
      console.error('❌ Erreur lors du chargement de l\'album:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement de l\'album');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gérer la sélection d'une photo
   */
  const handlePhotoSelect = (photoId: string) => {
    if (onPhotoSelect) {
      onPhotoSelect(photoId);
    }
  };

  /**
   * Effacer les données et recommencer
   */
  const clearData = () => {
    setPhotos([]);
    setAlbumUrl('');
    setError(null);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* En-tête */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-white mb-2">
          📸 Album Public Google Photos
        </h3>
        <p className="text-gray-400 text-sm">
          Collez le lien d'un album public Google Photos pour charger vos photos
        </p>
      </div>

      {/* Saisie du lien d'album */}
      <div className="space-y-3">
        <div className="flex flex-col sm:flex-row gap-2">
          <input
            type="url"
            value={albumUrl}
            onChange={(e) => setAlbumUrl(e.target.value)}
            placeholder="https://photos.app.goo.gl/..."
            className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d385f5] focus:border-transparent"
            disabled={loading}
          />
          <Button
            onClick={loadAlbumPhotos}
            disabled={loading || !albumUrl.trim()}
            isLoading={loading}
            className="sm:w-auto w-full"
          >
            {photos.length > 0 ? 'Recharger' : 'Charger'}
          </Button>
        </div>

        {/* Bouton d'effacement si des photos sont chargées */}
        {photos.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={clearData}
              className="text-gray-400 hover:text-white text-sm underline"
            >
              Changer d'album
            </button>
          </div>
        )}
      </div>

      {/* Message d'erreur */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-900/50 border border-red-500 rounded-lg p-3"
          >
            <p className="text-red-200 text-sm">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Grille de photos */}
      <AnimatePresence>
        {photos.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            {/* Compteur de photos */}
            <div className="flex justify-between items-center">
              <p className="text-gray-400 text-sm">
                {photos.length} photo{photos.length > 1 ? 's' : ''} trouvée{photos.length > 1 ? 's' : ''}
              </p>
              {selectedPhotos.size > 0 && (
                <p className="text-[#d385f5] text-sm font-medium">
                  {selectedPhotos.size} sélectionnée{selectedPhotos.size > 1 ? 's' : ''}
                </p>
              )}
            </div>

            {/* Grille de photos */}
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
              {photos.map((photo) => (
                <motion.div
                  key={photo.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                  className={`relative cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                    selectedPhotos.has(photo.id)
                      ? 'ring-2 ring-[#d385f5] ring-offset-2 ring-offset-[#0a0a0a] scale-95'
                      : 'hover:ring-1 hover:ring-gray-400 hover:scale-105'
                  }`}
                  onClick={() => handlePhotoSelect(photo.id)}
                >
                  <img
                    src={GooglePhotosPublicService.getThumbnailUrl(photo.baseUrl)}
                    alt={photo.filename}
                    className="w-full h-28 sm:h-32 object-cover"
                    loading="lazy"
                  />

                  {/* Indicateur de sélection */}
                  {selectedPhotos.has(photo.id) && (
                    <div className="absolute top-2 right-2 bg-[#d385f5] rounded-full p-1 shadow-lg">
                      <svg className="w-3 h-3 sm:w-4 sm:h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}

                  {/* Overlay au survol */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200" />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Instructions d'aide */}
      {photos.length === 0 && !loading && (
        <div className="text-center py-6">
          <div className="text-gray-500 text-sm space-y-2">
            <p>💡 <strong>Comment obtenir un lien d'album public ?</strong></p>
            <div className="text-xs space-y-1">
              <p>1. Ouvrez Google Photos et créez un album</p>
              <p>2. Cliquez sur "Partager" puis "Créer un lien"</p>
              <p>3. Copiez le lien et collez-le ci-dessus</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
