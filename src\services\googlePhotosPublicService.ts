import { GooglePhoto } from './googlePhotosService';

/**
 * Service pour gérer les albums publics Google Photos
 * Basé sur la méthode de scraping HTML découverte par Valentin Hervieu
 * https://medium.com/@ValentinHervieu/how-i-used-google-photos-to-host-my-website-pictures-gallery-d49f037c8e3c
 */
export class GooglePhotosPublicService {
  
  /**
   * Extraire l'ID d'album depuis un lien Google Photos public
   * @param url Lien de type https://photos.app.goo.gl/XYZ123
   * @returns ID de l'album ou null si invalide
   */
  static extractAlbumId(url: string): string | null {
    console.log('🔍 Extraction de l\'ID d\'album depuis:', url);
    
    try {
      // Nettoyer l'URL
      const cleanUrl = url.trim();
      
      // Vérifier le format Google Photos
      const googlePhotosRegex = /photos\.app\.goo\.gl\/([a-zA-Z0-9\-_]+)/;
      const match = cleanUrl.match(googlePhotosRegex);
      
      if (match && match[1]) {
        const albumId = match[1];
        console.log('✅ ID d\'album extrait:', albumId);
        return albumId;
      }
      
      console.log('❌ Format d\'URL invalide');
      return null;
      
    } catch (error) {
      console.error('❌ Erreur lors de l\'extraction de l\'ID:', error);
      return null;
    }
  }

  /**
   * Récupérer les photos d'un album public Google Photos
   * @param albumId ID de l'album extrait du lien
   * @returns Liste des photos de l'album
   */
  static async fetchAlbumPhotos(albumId: string): Promise<GooglePhoto[]> {
    console.log('📸 Récupération des photos de l\'album:', albumId);
    
    try {
      // Construire l'URL de l'album
      const albumUrl = `https://photos.app.goo.gl/${albumId}`;
      console.log('🌐 Fetch de la page:', albumUrl);
      
      // Récupérer le contenu HTML de l'album
      const response = await fetch(albumUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status} ${response.statusText}`);
      }
      
      const html = await response.text();
      console.log('✅ HTML récupéré, taille:', html.length, 'caractères');
      
      // Parser les photos depuis le HTML
      const photoUrls = this.parsePhotosFromHtml(html);
      console.log('📊 Photos trouvées:', photoUrls.length);
      
      // Convertir en format GooglePhoto
      const photos: GooglePhoto[] = photoUrls.map((url, index) => ({
        id: `public-${albumId}-${index}`,
        baseUrl: url,
        filename: `photo_${index + 1}.jpg`,
        mediaMetadata: {
          creationTime: new Date().toISOString(),
          width: '1024',
          height: '1024'
        },
        mimeType: 'image/jpeg'
      }));
      
      console.log('✅ Photos formatées:', photos.length);
      return photos;
      
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos:', error);
      throw new Error(`Impossible de récupérer les photos de l'album: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  /**
   * Parser les URLs des photos depuis le HTML de l'album
   * @param html Contenu HTML de la page d'album
   * @returns Liste des URLs des photos
   */
  static parsePhotosFromHtml(html: string): string[] {
    console.log('🔍 Parsing des photos depuis le HTML...');
    
    try {
      // Regex pour extraire les URLs des photos Google Photos
      // Basée sur l'article de Valentin Hervieu
      const regex = /\["(https:\/\/lh3\.googleusercontent\.com\/pw\/[a-zA-Z0-9\-_]*)"/g;
      
      const photoUrls = new Set<string>();
      let match;
      
      // Extraire toutes les URLs correspondantes
      while ((match = regex.exec(html)) !== null) {
        const url = match[1];
        if (url && url.length > 50) { // Filtrer les URLs trop courtes
          photoUrls.add(url);
        }
      }
      
      const uniqueUrls = Array.from(photoUrls);
      console.log('📸 URLs uniques extraites:', uniqueUrls.length);
      
      // Log des premières URLs pour debug
      if (uniqueUrls.length > 0) {
        console.log('🔍 Première URL:', uniqueUrls[0].substring(0, 100) + '...');
      }
      
      return uniqueUrls;
      
    } catch (error) {
      console.error('❌ Erreur lors du parsing HTML:', error);
      return [];
    }
  }

  /**
   * Valider qu'un lien est un album Google Photos public valide
   * @param url Lien à valider
   * @returns true si le lien est valide
   */
  static isValidGooglePhotosUrl(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false;
    }
    
    const cleanUrl = url.trim();
    return /^https:\/\/photos\.app\.goo\.gl\/[a-zA-Z0-9\-_]+$/.test(cleanUrl);
  }

  /**
   * Obtenir l'URL d'une photo avec une taille spécifique
   * @param baseUrl URL de base de la photo
   * @param size Taille souhaitée (ex: 'w300', 'w1024')
   * @returns URL avec paramètre de taille
   */
  static getPhotoWithSize(baseUrl: string, size: string = 'w300'): string {
    return `${baseUrl}=${size}`;
  }

  /**
   * Obtenir l'URL de thumbnail d'une photo
   * @param baseUrl URL de base de la photo
   * @returns URL du thumbnail
   */
  static getThumbnailUrl(baseUrl: string): string {
    return this.getPhotoWithSize(baseUrl, 'w300-h300-c');
  }

  /**
   * Obtenir l'URL haute résolution d'une photo
   * @param baseUrl URL de base de la photo
   * @returns URL haute résolution
   */
  static getHighResUrl(baseUrl: string): string {
    return this.getPhotoWithSize(baseUrl, 'w1024');
  }
}
