import { auth } from './api';

// Interface pour les photos Google Photos
export interface GooglePhoto {
  id: string;
  baseUrl: string;
  filename: string;
  mediaMetadata: {
    creationTime: string;
    width: string;
    height: string;
  };
  mimeType: string;
}

// Interface pour la réponse de l'API Google Photos
interface GooglePhotosResponse {
  mediaItems: GooglePhoto[];
  nextPageToken?: string;
}

/**
 * Service pour interagir avec l'API Google Photos
 * Utilise UNIQUEMENT Firebase Authentication
 */
export class GooglePhotosService {
  private static readonly BASE_URL = 'https://photoslibrary.googleapis.com/v1';
  private static isInitialized = false;

  /**
   * Initialiser le service Google Photos
   */
  static async initialize(): Promise<void> {
    console.log('🔧 Initialisation du service Google Photos...');
    this.isInitialized = true;
    console.log('✅ Service Google Photos initialisé');
  }

  /**
   * Obtenir le token d'accès Google OAuth pour Google Photos avec rafraîchissement automatique
   */
  private static async getAccessToken(): Promise<string | null> {
    const user = auth.currentUser;
    if (!user) {
      console.error('❌ Utilisateur non connecté à Firebase');
      return null;
    }

    try {
      // Récupérer le token d'accès Google depuis sessionStorage
      let accessToken = sessionStorage.getItem('google_access_token');
      const tokenTimestamp = sessionStorage.getItem('google_token_timestamp');

      // Vérifier si le token existe et n'est pas expiré (1 heure = 3600000ms)
      const now = Date.now();
      const tokenAge = tokenTimestamp ? now - parseInt(tokenTimestamp) : Infinity;
      const TOKEN_EXPIRY = 50 * 60 * 1000; // 50 minutes pour être sûr

      if (accessToken && tokenAge < TOKEN_EXPIRY) {
        console.log('✅ Token d\'accès Google valide récupéré');
        return accessToken;
      }

      // Token expiré ou manquant - tenter de le rafraîchir
      console.log('🔄 Token expiré ou manquant, rafraîchissement en cours...');

      try {
        // Forcer le rafraîchissement du token Firebase
        const freshToken = await user.getIdToken(true);

        // Récupérer un nouveau token Google via Firebase
        const newAccessToken = await this.refreshGoogleToken();

        if (newAccessToken) {
          // Stocker le nouveau token avec timestamp
          sessionStorage.setItem('google_access_token', newAccessToken);
          sessionStorage.setItem('google_token_timestamp', now.toString());
          console.log('✅ Token Google rafraîchi avec succès');
          return newAccessToken;
        }
      } catch (refreshError) {
        console.error('❌ Erreur lors du rafraîchissement du token:', refreshError);
      }

      console.log('⚠️ Impossible de rafraîchir le token - reconnexion nécessaire');
      return null;
    } catch (error) {
      console.error('❌ Erreur lors de l\'obtention du token Google:', error);
      return null;
    }
  }

  /**
   * Rafraîchir le token Google OAuth
   */
  private static async refreshGoogleToken(): Promise<string | null> {
    try {
      console.log('🔄 Tentative de rafraîchissement du token Google...');

      // Importer dynamiquement le provider depuis api.ts
      const { signInWithGoogle } = await import('./api');

      // Tenter une reconnexion silencieuse
      const result = await signInWithGoogle();

      if (result.success && result.accessToken) {
        console.log('✅ Token Google rafraîchi via reconnexion');
        return result.accessToken;
      }

      return null;
    } catch (error) {
      console.error('❌ Erreur lors du rafraîchissement du token Google:', error);
      return null;
    }
  }

  /**
   * Vérifier si l'utilisateur est connecté à Firebase
   */
  static async isSignedIn(): Promise<boolean> {
    const user = auth.currentUser;
    return user !== null;
  }

  /**
   * Se connecter à Google Photos (utilise l'authentification Firebase existante)
   */
  static async signIn(): Promise<void> {
    console.log('🔑 Vérification de la connexion Google Photos via Firebase...');

    const user = auth.currentUser;
    if (!user) {
      throw new Error('Utilisateur non connecté à Firebase. Veuillez vous connecter d\'abord.');
    }

    // Vérifier que l'utilisateur a les permissions Google Photos
    const hasPermission = await this.hasPhotosPermission();
    if (!hasPermission) {
      throw new Error('Permissions Google Photos manquantes. L\'authentification Firebase doit inclure les scopes Google Photos.');
    }

    console.log('✅ Connexion Google Photos confirmée');
  }

  /**
   * Vérifier si l'utilisateur a les permissions Google Photos
   */
  static async hasPhotosPermission(): Promise<boolean> {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ Aucun utilisateur Firebase connecté');
      return false;
    }

    try {
      console.log('🔍 Vérification des permissions Google Photos...');

      // Obtenir le token d'accès Firebase
      const token = await this.getAccessToken();
      if (!token) {
        console.log('❌ Impossible d\'obtenir le token Firebase');
        return false;
      }

      // Test avec l'endpoint mediaItems qui nécessite les permissions Google Photos
      console.log('🧪 Test d\'accès à l\'API Google Photos...');
      const response = await fetch(`${this.BASE_URL}/mediaItems?pageSize=1`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`📡 Réponse API Google Photos: ${response.status} ${response.statusText}`);

      if (response.ok) {
        console.log('✅ Permissions Google Photos confirmées');
        return true;
      } else {
        // Lire le détail de l'erreur
        const errorText = await response.text();
        console.log('❌ Erreur API Google Photos:', errorText);

        if (response.status === 403) {
          console.log('🔒 Erreur 403: Permissions insuffisantes - reconnexion nécessaire');
        }

        return false;
      }
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des permissions:', error);
      return false;
    }
  }

  /**
   * Effectuer un appel API avec retry automatique en cas d'erreur de token
   */
  private static async makeApiCall(url: string, maxRetries: number = 2): Promise<GooglePhotosResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Tentative ${attempt}/${maxRetries} pour l'API Google Photos`);

        // Obtenir le token (avec rafraîchissement automatique)
        const token = await this.getAccessToken();
        if (!token) {
          throw new Error('Impossible d\'obtenir le token Google');
        }

        // Appel API
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const data: GooglePhotosResponse = await response.json();
          console.log(`✅ Appel API réussi (tentative ${attempt})`);
          return data;
        }

        // Erreur d'autorisation - forcer le rafraîchissement du token
        if (response.status === 401 || response.status === 403) {
          console.log(`🔑 Erreur d'autorisation (${response.status}) - invalidation du token`);
          sessionStorage.removeItem('google_access_token');
          sessionStorage.removeItem('google_token_timestamp');

          if (attempt < maxRetries) {
            console.log('🔄 Nouvelle tentative avec token rafraîchi...');
            continue;
          }
        }

        // Autres erreurs
        const errorText = await response.text();
        console.error(`❌ Erreur API: ${response.status} ${response.statusText}`, errorText);
        lastError = new Error(`Erreur API Google Photos: ${response.status}`);

      } catch (error) {
        console.error(`❌ Erreur lors de la tentative ${attempt}:`, error);
        lastError = error as Error;

        if (attempt < maxRetries) {
          console.log('⏳ Attente avant nouvelle tentative...');
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw lastError || new Error('Échec de tous les appels API');
  }

  /**
   * Récupérer les photos récentes avec Firebase Auth et retry automatique
   */
  static async getRecentPhotos(pageSize: number = 50, hoursBack: number = 168): Promise<GooglePhoto[]> {
    console.log(`🔍 Récupération des photos Google Photos (${pageSize} photos, dernières ${hoursBack}h)...`);

    // Vérifier la connexion Firebase
    if (!await this.isSignedIn()) {
      throw new Error('Utilisateur non connecté à Firebase');
    }

    try {
      // Appel API avec retry automatique
      const data = await this.makeApiCall(`${this.BASE_URL}/mediaItems?pageSize=${pageSize}`);
      const mediaItems = data.mediaItems || [];

      console.log(`📊 ${mediaItems.length} photos totales récupérées depuis Google Photos`);

      if (mediaItems.length === 0) {
        console.log('⚠️ Aucune photo trouvée dans Google Photos');
        return [];
      }

      // Filtrer les photos récentes (par défaut 7 jours au lieu de 24h)
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - hoursBack);

      const recentPhotos = mediaItems.filter(item => {
        const creationTime = new Date(item.mediaMetadata.creationTime);
        return creationTime >= cutoffTime;
      });

      console.log(`📅 ${recentPhotos.length} photos récentes (dernières ${hoursBack}h = ${Math.round(hoursBack/24)} jours)`);

      // Si pas de photos récentes, retourner les 10 plus récentes
      if (recentPhotos.length === 0) {
        console.log('📸 Aucune photo récente, retour des 10 plus récentes');
        return mediaItems.slice(0, 10).map(item => ({
          id: item.id,
          baseUrl: item.baseUrl,
          filename: item.filename || `photo_${item.id}.jpg`,
          mediaMetadata: {
            creationTime: item.mediaMetadata.creationTime,
            width: item.mediaMetadata.width || '0',
            height: item.mediaMetadata.height || '0'
          },
          mimeType: item.mimeType || 'image/jpeg'
        }));
      }

      return recentPhotos.map(item => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos:', error);
      throw error;
    }
  }

  /**
   * Récupérer toutes les photos avec Firebase Auth et retry automatique
   */
  static async getAllPhotos(pageSize: number = 50): Promise<GooglePhoto[]> {
    console.log('🔍 Récupération de toutes les photos via Firebase...');

    // Vérifier la connexion Firebase
    if (!await this.isSignedIn()) {
      throw new Error('Utilisateur non connecté à Firebase');
    }

    try {
      // Appel API avec retry automatique
      const data = await this.makeApiCall(`${this.BASE_URL}/mediaItems?pageSize=${pageSize}`);
      const mediaItems = data.mediaItems || [];

      console.log(`✅ ${mediaItems.length} photos récupérées`);

      return mediaItems.map(item => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos:', error);
      throw error;
    }
  }

  /**
   * Récupérer les photos par type (utilise getAllPhotos)
   */
  static async getPhotosByType(searchTerms: string[] = ['plant', 'garden', 'flower'], pageSize: number = 30): Promise<GooglePhoto[]> {
    // Pour l'instant, retourner toutes les photos
    return this.getAllPhotos(pageSize);
  }

  /**
   * Fonction de debug pour diagnostiquer les problèmes d'API
   */
  static async debugApiStatus(): Promise<void> {
    console.log('🔧 === DEBUG GOOGLE PHOTOS API ===');

    // 1. Vérifier l'utilisateur Firebase
    const user = auth.currentUser;
    console.log('👤 Utilisateur Firebase:', user ? `✅ ${user.email}` : '❌ Non connecté');

    if (!user) return;

    // 2. Vérifier le token
    const token = sessionStorage.getItem('google_access_token');
    const tokenTimestamp = sessionStorage.getItem('google_token_timestamp');

    console.log('🔑 Token stocké:', token ? '✅ Présent' : '❌ Absent');

    if (tokenTimestamp) {
      const age = Date.now() - parseInt(tokenTimestamp);
      const ageMinutes = Math.round(age / (1000 * 60));
      console.log(`⏰ Âge du token: ${ageMinutes} minutes`);
    }

    // 3. Tester l'API directement
    if (token) {
      try {
        console.log('🧪 Test direct de l\'API Google Photos...');
        const response = await fetch(`${this.BASE_URL}/mediaItems?pageSize=5`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log(`📡 Statut API: ${response.status} ${response.statusText}`);

        if (response.ok) {
          const data = await response.json();
          console.log(`📊 Photos trouvées: ${data.mediaItems?.length || 0}`);

          if (data.mediaItems?.length > 0) {
            const firstPhoto = data.mediaItems[0];
            console.log('📸 Première photo:', {
              id: firstPhoto.id,
              filename: firstPhoto.filename,
              creationTime: firstPhoto.mediaMetadata?.creationTime
            });
          }
        } else {
          const errorText = await response.text();
          console.log('❌ Erreur API:', errorText);
        }
      } catch (error) {
        console.error('❌ Erreur lors du test API:', error);
      }
    }

    console.log('🔧 === FIN DEBUG ===');
  }
}
